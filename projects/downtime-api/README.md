# Downtime API

API for managing maintenance periods and downtime schedules. Keeps track of maintenance periods for sites that upstream services can query to determine if a site is currently in a maintenance period to avoid alerting.

## Overview

The Downtime API provides endpoints to manage maintenance periods that can be used to track scheduled downtime for various sites or services. It supports creating, reading, updating, and deleting maintenance periods with regex-based site ID matching.

## Features

- **CRUD Operations**: Create, read, update, and delete maintenance periods
- **Site ID Filtering**: Filter maintenance periods by site ID using regex patterns
- **Active Period Filtering**: Query for currently active maintenance periods
- **Environment Support**: Separate dev and prod environments with isolated DynamoDB tables

## API Endpoints

### GET /api/v1/maintenance
Get all maintenance periods with optional filtering.

**Query Parameters:**
- `site_id` (optional): Filter by site ID (supports regex matching)
- `active` (optional): Filter for currently active maintenance periods (boolean)

### GET /api/v1/maintenance/{id}
Get a specific maintenance period by ID.

### POST /api/v1/maintenance
Create a new maintenance period.

**Request Body:**
```json
{
  "start": "2024-01-01T10:00:00Z",
  "end": "2024-01-01T12:00:00Z",
  "site_id_regex": "site-.*"
}
```

### PUT /api/v1/maintenance
Update an existing maintenance period.

**Request Body:**
```json
{
  "id": "maintenance-period-id",
  "start": "2024-01-01T10:00:00Z",
  "end": "2024-01-01T12:00:00Z",
  "site_id_regex": "site-.*"
}
```

### DELETE /api/v1/maintenance
Delete a maintenance period.

**Request Body:**
```json
{
  "id": "maintenance-period-id"
}
```

## Infrastructure

The API is deployed using AWS SAM and includes:

- **Lambda Function**: Handles API requests using AWS Lambda Powertools
- **API Gateway**: REST API with CORS enabled
- **DynamoDB Table**: Stores maintenance periods with environment-specific naming
- **IAM Roles**: Appropriate permissions for DynamoDB access

## Development

### Local Testing

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run tests:
   ```bash
   pytest test_app.py -v
   ```

### Deployment

The API is deployed via Bitbucket Pipelines:

- **Dev Environment**: `deploy-downtime-api-dev`
- **Prod Environment**: `deploy-downtime-api-prod`

### Environment Variables

- `ENVIRONMENT`: Deployment environment (dev/prod)
- `MAINTENANCE_PERIODS_TABLE`: DynamoDB table name
- `POWERTOOLS_SERVICE_NAME`: Service name for logging
- `POWERTOOLS_LOG_LEVEL`: Log level (DEBUG/INFO/WARNING/ERROR/CRITICAL)
- `POWERTOOLS_LOGGER_LOG_EVENT`: Whether to log events (true/false)

## Configuration

The SAM template includes the following configurable parameters:

- `Environment`: Deployment environment (dev/prod)
- `LogLevel`: Lambda function log level
- `LogEvent`: Whether to log Lambda events

## Data Model

### MaintenancePeriod

- `id` (String): Unique identifier for the maintenance period
- `start` (DateTime): Start time of the maintenance period
- `end` (DateTime, optional): End time of the maintenance period
- `site_id_regex` (String, optional): Regex pattern to match site IDs
