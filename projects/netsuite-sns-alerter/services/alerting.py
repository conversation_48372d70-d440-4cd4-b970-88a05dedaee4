from aws_lambda_powertools import Logger
from netsuite.exceptions import NetsuiteAPIRequestError

from models import Alert
from services.netsuite import SupportCase
from utils import generate_stable_eid

from typing import TYPE_CHECKING

if TYPE_CHECKING: # Prevent circular import
    from context import Context


logger = Logger()


class InvalidStatusError(Exception): ...


class AlertHandler:
    def __init__(self, context: "Context") -> None:
        self._context = context

    async def process_alert(self, alert: Alert) -> None:
        if self._context.downtime_service.is_site_alerts_disabled(alert.site_id):
            return

        if alert.status == "firing":
            await self._process_firing_alert(alert)

        elif alert.status == "resolved":
            await self._process_resolved_alert(alert)

        else:
            raise InvalidStatusError(f"Unknown alert status: {alert.status}")

    async def _process_firing_alert(self, alert: Alert) -> None:
        logger.debug("Processing firing alert", extra={"alert": alert.model_dump(mode="json")})
        eid = generate_stable_eid(alert)
        await self._context.netsuite.create_support_case(
            SupportCase(
                eid=eid,
                title=alert.title,
                site_id=alert.site_id,
                incoming_message=alert.message,
                company=self._context.netsuite_fields.NETSUITE_COMPANY,
                status=self._context.netsuite_fields.NETSUITE_STATUS_PENDING,
                origin=self._context.netsuite_fields.NETSUITE_ORIGIN_PLATFORM,
                category=self._context.netsuite_fields.NETSUITE_CATEGORY_L2_CAMERA_OFFLINE,
                priority=self._context.netsuite_fields.NETSUITE_PRIORITY_P3,
                product=self._context.netsuite_fields.NETSUITE_PRODUCT_EYECUE,
            )
        )

    async def _process_resolved_alert(self, alert: Alert) -> None:
        logger.debug("Processing resolved alert", extra={"alert": alert.model_dump(mode="json")})
        eid = generate_stable_eid(alert)
        try:
            await self._context.netsuite.update_support_case(
                eid=eid,
                status=self._context.netsuite_fields.NETSUITE_STATUS_CLOSED,
            )
        except NetsuiteAPIRequestError as e:
            if e.status_code == 404:
                logger.warning(
                    "Could not close support case - case not found. It may have been manually closed or deleted.",
                    extra={
                        "alert": alert.model_dump(mode="json"),
                        "eid": eid,
                        "status_code": e.status_code,
                    }
                )
            else:
                raise
